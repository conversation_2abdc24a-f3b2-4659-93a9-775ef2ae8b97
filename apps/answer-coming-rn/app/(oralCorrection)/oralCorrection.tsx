import { YTTouchable, YTYStack } from '@bookln/cross-platform-components';
import { ChevronLeftIcon } from '@bookln/icon-lucide';
import { Stack, router } from 'expo-router';
import { useCallback, useMemo } from 'react';
import { OralCorrectionCamera } from '../../components/oralCorrection/OralCorrectionCamera';
import { OralCorrectionResult } from '../../components/oralCorrection/OralCorrectionResult';
import {
  OralCorrectionProgress,
  useOralCorrection,
} from '../../hooks/useOralCorrection';

/**
 * 口算批改页面
 */
const OralCorrectionScreen = () => {
  const { progress, loading, checkDTO, submitCorrection, resetCorrection } =
    useOralCorrection();

  const handleTakePhoto = useCallback(
    (imageUri: string) => {
      submitCorrection(imageUri);
    },
    [submitCorrection],
  );

  const handleRetake = useCallback(() => {
    resetCorrection();
  }, [resetCorrection]);

  const handleViewHistory = useCallback(() => {
    router.push('/(oralCorrection)/oralCorrectionRecord');
  }, []);

  const onPressBack = useCallback(() => {
    router.back();
  }, []);

  const renderContent = useMemo(() => {
    switch (progress) {
      case OralCorrectionProgress.Camera:
        return (
          <OralCorrectionCamera
            onTakePhoto={handleTakePhoto}
            loading={loading}
          />
        );

      case OralCorrectionProgress.Processing:
      case OralCorrectionProgress.Result:
        return (
          <OralCorrectionResult
            visible={true}
            checkDTO={checkDTO}
            loading={loading}
            onRetake={handleRetake}
            onViewHistory={handleViewHistory}
          />
        );

      default:
        return (
          <OralCorrectionCamera
            onTakePhoto={handleTakePhoto}
            loading={loading}
          />
        );
    }
  }, [
    checkDTO,
    handleRetake,
    handleTakePhoto,
    handleViewHistory,
    loading,
    progress,
  ]);

  return (
    <YTYStack flex={1} bg='white'>
      <Stack.Screen
        options={{
          title: '口算批改',
          headerBackTitle: '返回',
          headerStyle: {
            backgroundColor: 'black',
          },
          headerTintColor: 'white',
          headerLeft: () => {
            return (
              <YTTouchable onPress={onPressBack}>
                <ChevronLeftIcon color={'white'} />
              </YTTouchable>
            );
          },
        }}
      />

      {renderContent}
    </YTYStack>
  );
};

export default OralCorrectionScreen;
