import { YTYStack } from '@bookln/cross-platform-components';
import { useSafeAreaInsets } from '@jgl/biz-func';
import { container } from '@jgl/container';
import { Stack, router, useLocalSearchParams } from 'expo-router';
import { useCallback, useEffect, useState } from 'react';
import { queryUserCorrectDetailByRecordId } from '../../api/api';
import { OralCorrectionResult } from '../../components/oralCorrection/OralCorrectionResult';
import { OralCorrectionResult as ResultType } from '../../hooks/useOralCorrection';

/**
 * 口算批改结果页面 - 独立页面，用于查看历史批改记录
 */
const OralCorrectionResultScreen = () => {
  const { bottom } = useSafeAreaInsets();
  const { recordId } = useLocalSearchParams<{ recordId: string }>();
  const [checkDTO, setCheckDTO] = useState<ResultType | undefined>();
  const [loading, setLoading] = useState(false);
  const [intervalTime, setIntervalTime] = useState<number | undefined>();

  // 获取批改结果 - 完全参照小程序版本的逻辑
  const getResult = useCallback(async () => {
    if (!recordId) {
      return;
    }

    try {
      const res = await container.net().fetch(
        queryUserCorrectDetailByRecordId({
          recordId: String(recordId),
        }),
      );

      if (res.success && res.data) {
        // 先检查是否有错误码
        if (res.data.errorCode) {
          setCheckDTO({
            errorMsg: res.data.errorMsg || '批改失败，请重试',
          });
          setLoading(false);
          setIntervalTime(undefined);
          return;
        }

        // 批改成功清除定时器
        if (res.data.resultInfos?.length) {
          setIntervalTime(undefined);
          setLoading(false);
          setCheckDTO(res.data);
        }
      }
    } catch (error) {
      console.error('获取批改结果失败:', error);
      setCheckDTO({
        errorMsg: '网络错误，请重试',
      });
      setLoading(false);
      setIntervalTime(undefined);
    }
  }, [recordId]);

  useEffect(() => {
    if (intervalTime) {
      const interval = setInterval(getResult, intervalTime);
      return () => clearInterval(interval);
    }
  }, [getResult, intervalTime]);

  useEffect(() => {
    if (recordId) {
      setLoading(true);
      setIntervalTime(1000);
    }
  }, [recordId]);

  // 处理按钮回调
  const handleViewHistory = useCallback(() => {
    router.push('/(oralCorrection)/oralCorrectionRecord');
  }, []);

  const handleRetake = useCallback(() => {
    router.back();
  }, []);

  return (
    <YTYStack flex={1} bg='white' style={{ paddingBottom: bottom || 16 }}>
      <Stack.Screen
        options={{
          title: '批改结果',
          headerBackTitle: '返回',
        }}
      />

      <OralCorrectionResult
        visible={true}
        checkDTO={checkDTO}
        loading={loading}
        onRetake={handleRetake}
        onViewHistory={handleViewHistory}
      />
    </YTYStack>
  );
};

export default OralCorrectionResultScreen;
